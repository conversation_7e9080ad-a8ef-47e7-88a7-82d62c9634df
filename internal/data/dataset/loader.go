// Package dataset provides functionality for loading and managing datasets
// from various file formats. It serves as a domain-specific layer that
// builds upon the raw format parsers to provide higher-level dataset operations.
package dataset

import (
	"fmt"

	"github.com/berrijam/mulberri/internal/data/features"
	"github.com/berrijam/mulberri/internal/io/formats/csv"
	"github.com/berrijam/mulberri/internal/utils/logger"
)

// Loader handles loading CSV data and basic type conversion.
// It provides a domain-specific interface for dataset loading operations
// while delegating the actual parsing to format-specific parsers.
//
// The Loader follows the separation of concerns principle where:
// - Format layer (io/formats/) handles raw parsing
// - Loader layer (data/*/loader.go) provides domain-specific loading
// - Future converter layer will handle type conversion
// - Future validation layer will handle business rules
type Loader struct {
	// csvParser handles the actual CSV file parsing operations
	csvParser *csv.Parser
}

// NewLoader creates a new dataset loader with default CSV parser settings.
// The returned loader is ready to use for loading CSV files.
//
// Returns:
//   - *Loader: A new loader instance configured with default settings
//
// Example:
//
//	loader := NewLoader()
//	data, err := loader.LoadCSV("data.csv")
func NewLoader() *Loader {
	return &Loader{
		csvParser: csv.NewParser(),
	}
}

// LoadCSV loads a CSV file and returns the parsed data as CSVData.
// The first row of the CSV is always treated as headers, and subsequent
// rows are treated as data records.
//
// Parameters:
//   - csvPath: The file path to the CSV file to load
//
// Returns:
//   - *csv.CSVData: The parsed CSV data containing headers and records
//   - error: An error if the file cannot be opened or parsed
//
// The returned CSVData contains:
//   - Headers: Column names from the first row
//   - Records: Data rows as string slices
//   - NumColumns: Number of columns in the dataset
//   - NumRows: Number of data rows (excluding header)
//
// Memory Management:
// When converting CSVData to a typed Dataset structure, ensure to call
// CSVData.Release() or set the CSVData pointer to nil after conversion
// to avoid double memory consumption. The CSVData holds all string data
// in memory, and the converted Dataset will hold typed data, potentially
// doubling memory usage if both are retained.
//
// Example:
//
//	loader := NewLoader()
//	csvData, err := loader.LoadCSV("data.csv")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Loaded %d rows with %d columns\n", csvData.NumRows, csvData.NumColumns)
//
//	// Convert to typed Dataset (future implementation)
//	// dataset := ConvertToDataset(csvData, featureInfo)
//
//	// Free CSVData memory after conversion
//	// csvData.Release()
//	// csvData = nil
//
// Errors:
//   - File not found or permission errors
//   - CSV parsing errors (malformed CSV, inconsistent column counts)
//   - Empty file errors
func (l *Loader) LoadCSV(csvPath string) *csv.CSVData {
	return l.csvParser.ParseCsvFile(csvPath)
}

// LoadCSVToDataset loads a CSV file and converts it directly to a typed Dataset.
//
// This function integrates CSV loading with type conversion using the ConvertValue function.
// It requires feature metadata to be provided for proper type conversion.
//
// Args:
// - csvPath: Path to the CSV file to load
// - featureMetadata: Map of feature names to their types for conversion
// - targetColumn: Name of the target column (will be excluded from features)
//
// Returns:
// - *Dataset[T]: Typed dataset with converted values
// - error: Error if loading or conversion fails
//
// The method performs the following steps:
// 1. Load raw CSV data using existing CSV parser
// 2. Identify feature columns (all columns except target)
// 3. Convert string values to appropriate types using ConvertValue
// 4. Create typed columns and populate dataset
// 5. Set feature metadata for each column
//
// Memory Management:
// The raw CSV data is released after conversion to avoid double memory usage.
//
// Example:
//
//	featureTypes := map[string]features.FeatureType{
//	    "age": features.IntegerFeature,
//	    "salary": features.FloatFeature,
//	    "department": features.StringFeature,
//	}
//	dataset, err := LoadCSVToDataset[string]("data.csv", featureTypes, "class")
//
// LoadCSVToDataset loads a CSV file and converts it directly to a typed Dataset.
// This is a standalone function that creates its own loader instance.
func LoadCSVToDataset[T comparable](csvPath string, featureMetadata map[string]features.FeatureType, targetColumn string) (*Dataset[T], error) {
	loader := NewLoader()
	// Step 1: Load raw CSV data
	csvData := loader.LoadCSV(csvPath)
	if csvData == nil {
		return nil, fmt.Errorf("failed to load CSV data from %s", csvPath)
	}
	defer csvData.Release() // Clean up raw CSV data after conversion

	// Step 2: Validate target column exists
	targetColumnIndex := -1
	for i, header := range csvData.Headers {
		if header == targetColumn {
			targetColumnIndex = i
			break
		}
	}
	if targetColumnIndex == -1 {
		return nil, fmt.Errorf("target column '%s' not found in CSV headers", targetColumn)
	}

	// Step 3: Create dataset with appropriate capacity
	dataset := NewDataset[T](csvData.NumRows)

	// Step 4: Process each feature column (excluding target)
	for colIndex, featureName := range csvData.Headers {
		if colIndex == targetColumnIndex {
			continue // Skip target column
		}

		// Get feature type from metadata
		featureType, exists := featureMetadata[featureName]
		if !exists {
			logger.Warn(fmt.Sprintf("Feature type not specified for column '%s', defaulting to StringFeature", featureName))
			featureType = features.StringFeature
		}

		// Convert column data and create appropriate column type
		err := convertAndAddColumn(dataset, csvData, colIndex, featureName, featureType)
		if err != nil {
			return nil, fmt.Errorf("failed to convert column '%s': %v", featureName, err)
		}

		// Set feature metadata
		featureInfo := features.NewFeatureInfo(featureName, featureType, featureType.String())
		dataset.SetFeatureInfo(featureName, featureInfo)
	}

	// Step 5: Process target column
	err := convertAndAddTargets(dataset, csvData, targetColumnIndex)
	if err != nil {
		return nil, fmt.Errorf("failed to convert target column '%s': %v", targetColumn, err)
	}

	logger.Info(fmt.Sprintf("Successfully loaded dataset: %d rows, %d features", dataset.totalSize, len(dataset.GetFeatureOrder())))
	return dataset, nil
}

// convertAndAddColumn converts a CSV column to the appropriate type and adds it to the dataset.
func convertAndAddColumn[T comparable](dataset *Dataset[T], csvData *csv.CSVData, colIndex int, featureName string, featureType features.FeatureType) error {
	numRows := csvData.NumRows

	switch featureType {
	case features.IntegerFeature:
		data := make([]int64, numRows)
		nullMask := make([]bool, numRows)

		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
			} else {
				data[rowIndex] = convertedValue.(int64)
				nullMask[rowIndex] = false
			}
		}

		return dataset.AddIntColumn(featureName, data, nullMask)

	case features.FloatFeature:
		data := make([]float64, numRows)
		nullMask := make([]bool, numRows)

		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
			} else {
				data[rowIndex] = convertedValue.(float64)
				nullMask[rowIndex] = false
			}
		}

		return dataset.AddFloatColumn(featureName, data, nullMask)

	case features.StringFeature:
		data := make([]string, numRows)
		nullMask := make([]bool, numRows)

		for rowIndex := 0; rowIndex < numRows; rowIndex++ {
			rawValue := csvData.Records[rowIndex][colIndex]
			convertedValue := features.ConvertValue(rawValue, featureType)

			if convertedValue == nil {
				nullMask[rowIndex] = true
			} else {
				data[rowIndex] = convertedValue.(string)
				nullMask[rowIndex] = false
			}
		}

		return dataset.AddStringColumn(featureName, data, nullMask)

	default:
		return fmt.Errorf("unsupported feature type: %v", featureType)
	}
}

// convertAndAddTargets converts the target column values and adds them to the dataset.
// Note: This is a simplified implementation that assumes T is string.
// For other target types, additional conversion logic would be needed.
func convertAndAddTargets[T comparable](dataset *Dataset[T], csvData *csv.CSVData, targetColumnIndex int) error {
	for rowIndex := 0; rowIndex < csvData.NumRows; rowIndex++ {
		rawTargetValue := csvData.Records[rowIndex][targetColumnIndex]

		// Simple conversion - assumes T is string type
		// For production use, you'd want more sophisticated type conversion
		targetValue, ok := any(rawTargetValue).(T)
		if !ok {
			return fmt.Errorf("cannot convert target value '%s' to target type %T", rawTargetValue, targetValue)
		}

		dataset.AddTarget(targetValue)
	}

	return nil
}
