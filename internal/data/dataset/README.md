# Dataset Package

The dataset package provides efficient column-based data structures for machine learning datasets with support for subset operations through views.

## Architecture

### Core Components

- **`Dataset[T]`**: Full dataset storage with typed columns
- **`DatasetView[T]`**: Efficient subset views without data duplication  
- **`FeatureColumn`**: Unified interface for different column types
- **Column Types**: `IntColumn`, `FloatColumn`, `StringColumn`

### Design Principles

1. **Column-based Storage**: Data stored by feature type for efficient access
2. **View-based Subsets**: Views reference parent dataset without copying data
3. **Type Safety**: Separate column implementations with unified interface
4. **Immutable Design**: Datasets and views don't change after creation
5. **Logical Indexing**: Views use logical indices (0 to size-1) that map to physical indices

## Usage Patterns

### Basic Dataset Creation

```go
// Create dataset with capacity hint
dataset := NewDataset[string](1000)

// Add typed columns
ages := []int64{25, 30, 35, 40, 45}
nullMask := []bool{false, false, false, false, false}
err := dataset.AddIntColumn("age", ages, nullMask)

salaries := []float64{50000.0, 60000.0, 70000.0, 80000.0, 90000.0}
err = dataset.AddFloatColumn("salary", salaries, nullMask)

// Add targets
targets := []string{"A", "B", "A", "B", "A"}
for _, target := range targets {
    dataset.AddTarget(target)
}
```

### Working with Views

```go
// Create view with specific rows
view := dataset.CreateView([]int{0, 2, 4}) // physical indices 0, 2, 4

// Access data using logical indices
value, err := view.GetFeatureValue(0, "age") // logical index 0 -> physical index 0
target, err := view.GetTarget(1)             // logical index 1 -> physical index 2

// Get target distribution (cached)
dist, err := view.GetTargetDistribution() // {"A": 3, "B": 0}

// Create child view from parent view
childView := view.CreateChildView([]int{0, 2}) // logical indices 0, 2 -> physical indices 0, 4
```

### Index Translation

Understanding the index mapping is crucial:

```go
dataset := NewDataset[string](5) // indices 0, 1, 2, 3, 4
view := dataset.CreateView([]int{1, 3, 4}) // physical indices 1, 3, 4

// Logical to Physical mapping in view:
// logical 0 -> physical 1
// logical 1 -> physical 3  
// logical 2 -> physical 4

value, _ := view.GetFeatureValue(0, "age") // Gets dataset row 1
value, _ := view.GetFeatureValue(1, "age") // Gets dataset row 3
value, _ := view.GetFeatureValue(2, "age") // Gets dataset row 4

// Child view from parent view
child := view.CreateChildView([]int{0, 2}) // logical indices in view
// Child's physical indices: [1, 4] (from parent view's indices)
```

## Caching Strategy

### Target Distribution Caching

DatasetView caches target distribution calculations for performance:

```go
view := dataset.CreateView([]int{0, 1, 2})

// First call: calculates and caches (targetDistDirty: true -> false)
dist1, _ := view.GetTargetDistribution() // O(n) calculation

// Subsequent calls: returns cached result (targetDistDirty: false)
dist2, _ := view.GetTargetDistribution() // O(1) cache hit
```

### When Cache is Marked Dirty

- **View Creation**: `CreateView()` and `CreateChildView()` mark cache dirty
- **After Calculation**: `GetTargetDistribution()` marks cache clean
- **No Invalidation**: Views are immutable, so cache never needs invalidation

## Memory Management

### Efficient Memory Usage

- **Dataset**: Stores full data in typed columns
- **DatasetView**: Stores only indices and cached calculations
- **No Duplication**: Views reference parent dataset, no data copying

### Memory Patterns

```go
dataset := NewDataset[string](10000) // ~80KB for 10K rows
view1 := dataset.CreateView(indices1) // ~40 bytes + indices
view2 := dataset.CreateView(indices2) // ~40 bytes + indices
// Total memory ≈ dataset size + small view overhead
```

## Performance Characteristics

| Operation | Complexity | Notes |
|-----------|------------|-------|
| Dataset Creation | O(1) | Pre-allocates target slice |
| Add Column | O(1) | Hash map insertion |
| Create View | O(1) | Only stores indices |
| Feature Access | O(1) | Direct column access |
| Target Distribution | O(n) first call, O(1) cached | Where n = view size |
| Child View Creation | O(k) | Where k = number of child indices |

## Error Handling

### Common Error Patterns

```go
// Index out of bounds
value, err := view.GetFeatureValue(999, "age") // logical index >= view.size

// Feature not found  
value, err := view.GetFeatureValue(0, "nonexistent") // feature doesn't exist

// Column length mismatch
err := dataset.AddIntColumn("age", data, wrongSizeNullMask) // lengths don't match

// String numerical conversion
col, _ := dataset.GetColumn("department") // string column
numVal, err := col.GetNumericalValue(0) // error: strings don't convert
```

## Thread Safety

- **Read Operations**: Safe for concurrent access
- **Write Operations**: Not thread-safe, require external synchronization
- **Views**: Independent, safe for concurrent read access

## Integration with ML Algorithms

The dataset package is designed for decision tree algorithms:

```go
// Split dataset for decision tree node
leftIndices := []int{0, 2, 4}
rightIndices := []int{1, 3, 5}

leftView := parentView.CreateChildView(leftIndices)
rightView := parentView.CreateChildView(rightIndices)

// Get target distributions for information gain calculation
// These return map[T]int where T is the target type (e.g., string)
leftTargetDist, _ := leftView.GetTargetDistribution()   // map[string]int
rightTargetDist, _ := rightView.GetTargetDistribution() // map[string]int

// Get feature distributions for split point evaluation
// These return *features.Distribution with sorted value entries
leftFeatureDist, _ := leftView.GetFeatureDistribution("age")   // *features.Distribution
rightFeatureDist, _ := rightView.GetFeatureDistribution("age") // *features.Distribution
```

### Distribution Types

The dataset package works with two distinct types of distributions:

1. **Target Distributions** (`map[T]int`): Used for calculating entropy and information gain
   - Maps target class values to their occurrence counts
   - Example: `map[string]int{"A": 3, "B": 2}` for 3 "A" targets and 2 "B" targets
   - Returned by `GetTargetDistribution()` method

2. **Feature Distributions** (`*features.Distribution`): Used for finding split points
   - Maintains sorted entries of feature values with counts
   - Supports efficient range queries and split point identification
   - Works with int64, float64, and string feature types
   - Returned by `GetFeatureDistribution()` method (future implementation)

Both distribution types are essential for decision tree algorithms but serve different purposes in the split evaluation process.

## Dependencies

- **Go Standard Library**: `fmt`, `slices`
- **Internal Packages**: `github.com/berrijam/mulberri/internal/data/features`
- **External**: None

## Testing

Run tests with:
```bash
go test ./internal/data/dataset -v
```

Test coverage includes:
- Dataset creation and column addition
- View creation and data access
- Index translation and bounds checking
- Target distribution caching
- Error conditions and edge cases
