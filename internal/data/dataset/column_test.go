package dataset

import (
	"testing"

	"github.com/berrijam/mulberri/internal/data/features"
)

// TestIntColumn_GetValue tests IntColumn GetValue method
func TestIntColumn_GetValue(t *testing.T) {
	tests := []struct {
		name     string
		data     []int64
		nullMask []bool
		index    int
		expected any
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid value at index 0",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    0,
			expected: int64(10),
			wantErr:  false,
		},
		{
			name:     "valid value at index 2",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    2,
			expected: int64(30),
			wantErr:  false,
		},
		{
			name:     "null value",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, true, false},
			index:    1,
			expected: nil,
			wantErr:  true,
			errMsg:   "missing value",
		},
		{
			name:     "index out of bounds",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    3,
			expected: nil,
			wantErr:  true,
			errMsg:   "index out of bounds",
		},
		{
			name:     "negative index",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    -1,
			expected: nil,
			wantErr:  true,
			errMsg:   "index out of bounds",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			col := &IntColumn{
				data:     tt.data,
				nullMask: tt.nullMask,
			}

			result, err := col.GetValue(tt.index)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("Expected error %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}

// TestIntColumn_GetNumericalValue tests IntColumn GetNumericalValue method
func TestIntColumn_GetNumericalValue(t *testing.T) {
	tests := []struct {
		name     string
		data     []int64
		nullMask []bool
		index    int
		expected float64
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid conversion",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    1,
			expected: 20.0,
			wantErr:  false,
		},
		{
			name:     "large integer conversion",
			data:     []int64{9223372036854775807}, // max int64
			nullMask: []bool{false},
			index:    0,
			expected: 9223372036854775807.0,
			wantErr:  false,
		},
		{
			name:     "null value",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, true, false},
			index:    1,
			expected: 0,
			wantErr:  true,
			errMsg:   "missing value",
		},
		{
			name:     "index out of bounds",
			data:     []int64{10, 20, 30},
			nullMask: []bool{false, false, false},
			index:    5,
			expected: 0,
			wantErr:  true,
			errMsg:   "index out of bounds",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			col := &IntColumn{
				data:     tt.data,
				nullMask: tt.nullMask,
			}

			result, err := col.GetNumericalValue(tt.index)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("Expected error %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}

// TestIntColumn_Methods tests IntColumn metadata methods
func TestIntColumn_Methods(t *testing.T) {
	col := &IntColumn{
		data:     []int64{1, 2, 3, 4, 5},
		nullMask: []bool{false, true, false, true, false},
	}

	// Test GetSize
	if size := col.GetSize(); size != 5 {
		t.Errorf("Expected size 5, got %d", size)
	}

	// Test GetType
	if colType := col.GetType(); colType != features.IntegerFeature {
		t.Errorf("Expected IntegerFeature, got %v", colType)
	}

	// Test IsNumerical
	if !col.IsNumerical() {
		t.Error("Expected IntColumn to be numerical")
	}
}

// TestFloatColumn_GetValue tests FloatColumn GetValue method
func TestFloatColumn_GetValue(t *testing.T) {
	tests := []struct {
		name     string
		data     []float64
		nullMask []bool
		index    int
		expected any
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid float value",
			data:     []float64{1.5, 2.7, 3.14},
			nullMask: []bool{false, false, false},
			index:    2,
			expected: 3.14,
			wantErr:  false,
		},
		{
			name:     "null value",
			data:     []float64{1.5, 2.7, 3.14},
			nullMask: []bool{false, true, false},
			index:    1,
			expected: nil,
			wantErr:  true,
			errMsg:   "missing value",
		},
		{
			name:     "index out of bounds",
			data:     []float64{1.5, 2.7, 3.14},
			nullMask: []bool{false, false, false},
			index:    10,
			expected: nil,
			wantErr:  true,
			errMsg:   "index out of bounds",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			col := &FloatColumn{
				data:     tt.data,
				nullMask: tt.nullMask,
			}

			result, err := col.GetValue(tt.index)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("Expected error %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}

// TestFloatColumn_GetNumericalValue tests FloatColumn GetNumericalValue method
func TestFloatColumn_GetNumericalValue(t *testing.T) {
	tests := []struct {
		name     string
		data     []float64
		nullMask []bool
		index    int
		expected float64
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "direct float value",
			data:     []float64{1.5, 2.7, 3.14},
			nullMask: []bool{false, false, false},
			index:    0,
			expected: 1.5,
			wantErr:  false,
		},
		{
			name:     "null value",
			data:     []float64{1.5, 2.7, 3.14},
			nullMask: []bool{true, false, false},
			index:    0,
			expected: 0,
			wantErr:  true,
			errMsg:   "missing value",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			col := &FloatColumn{
				data:     tt.data,
				nullMask: tt.nullMask,
			}

			result, err := col.GetNumericalValue(tt.index)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("Expected error %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}

// TestFloatColumn_Methods tests FloatColumn metadata methods
func TestFloatColumn_Methods(t *testing.T) {
	col := &FloatColumn{
		data:     []float64{1.1, 2.2, 3.3},
		nullMask: []bool{false, true, false},
	}

	// Test GetSize
	if size := col.GetSize(); size != 3 {
		t.Errorf("Expected size 3, got %d", size)
	}

	// Test GetType
	if colType := col.GetType(); colType != features.FloatFeature {
		t.Errorf("Expected FloatFeature, got %v", colType)
	}

	// Test IsNumerical
	if !col.IsNumerical() {
		t.Error("Expected FloatColumn to be numerical")
	}
}

// TestStringColumn_GetValue tests StringColumn GetValue method
func TestStringColumn_GetValue(t *testing.T) {
	tests := []struct {
		name     string
		data     []string
		nullMask []bool
		index    int
		expected any
		wantErr  bool
		errMsg   string
	}{
		{
			name:     "valid string value",
			data:     []string{"apple", "banana", "cherry"},
			nullMask: []bool{false, false, false},
			index:    1,
			expected: "banana",
			wantErr:  false,
		},
		{
			name:     "empty string is valid",
			data:     []string{"", "banana", "cherry"},
			nullMask: []bool{false, false, false},
			index:    0,
			expected: "",
			wantErr:  false,
		},
		{
			name:     "null value",
			data:     []string{"apple", "banana", "cherry"},
			nullMask: []bool{false, true, false},
			index:    1,
			expected: nil,
			wantErr:  true,
			errMsg:   "missing value",
		},
		{
			name:     "index out of bounds",
			data:     []string{"apple", "banana", "cherry"},
			nullMask: []bool{false, false, false},
			index:    5,
			expected: nil,
			wantErr:  true,
			errMsg:   "index out of bounds",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			col := &StringColumn{
				data:     tt.data,
				nullMask: tt.nullMask,
			}

			result, err := col.GetValue(tt.index)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("Expected error %q, got %q", tt.errMsg, err.Error())
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
					return
				}
				if result != tt.expected {
					t.Errorf("Expected %v, got %v", tt.expected, result)
				}
			}
		})
	}
}

// TestStringColumn_Methods tests StringColumn metadata methods
func TestStringColumn_Methods(t *testing.T) {
	col := &StringColumn{
		data:     []string{"red", "green", "blue", "yellow"},
		nullMask: []bool{false, true, false, false},
	}

	// Test GetSize
	if size := col.GetSize(); size != 4 {
		t.Errorf("Expected size 4, got %d", size)
	}

	// Test GetType
	if colType := col.GetType(); colType != features.StringFeature {
		t.Errorf("Expected StringFeature, got %v", colType)
	}

	// Test IsNumerical
	if col.IsNumerical() {
		t.Error("Expected StringColumn to not be numerical")
	}
}

// TestFeatureColumn_Interface tests that numerical column types implement FeatureColumn interface
func TestFeatureColumn_Interface(t *testing.T) {
	var _ FeatureColumn = &IntColumn{}
	var _ FeatureColumn = &FloatColumn{}
	// Note: StringColumn doesn't implement GetNumericalValue, so it doesn't fully implement FeatureColumn
}

// TestStringColumn_GetNumericalValue tests that StringColumn doesn't have GetNumericalValue
func TestStringColumn_GetNumericalValue(t *testing.T) {
	// This test documents that StringColumn intentionally doesn't implement GetNumericalValue
	// since you removed it from the implementation. This is by design for type-specific splitting.
	col := &StringColumn{
		data:     []string{"apple", "banana"},
		nullMask: []bool{false, false},
	}

	// StringColumn should not have GetNumericalValue method
	// This test serves as documentation of the design decision
	_ = col // Use the variable to avoid unused variable error
}

// TestColumn_EdgeCases tests edge cases and boundary conditions
func TestColumn_EdgeCases(t *testing.T) {
	t.Run("empty columns", func(t *testing.T) {
		intCol := &IntColumn{data: []int64{}, nullMask: []bool{}}
		floatCol := &FloatColumn{data: []float64{}, nullMask: []bool{}}
		stringCol := &StringColumn{data: []string{}, nullMask: []bool{}}

		// Test GetSize on empty columns
		if intCol.GetSize() != 0 {
			t.Error("Expected empty IntColumn size to be 0")
		}
		if floatCol.GetSize() != 0 {
			t.Error("Expected empty FloatColumn size to be 0")
		}
		if stringCol.GetSize() != 0 {
			t.Error("Expected empty StringColumn size to be 0")
		}

		// Test GetValue on empty columns should return out of bounds error
		_, err := intCol.GetValue(0)
		if err == nil || err.Error() != "index out of bounds" {
			t.Error("Expected out of bounds error for empty IntColumn")
		}
	})

	t.Run("single element columns", func(t *testing.T) {
		intCol := &IntColumn{data: []int64{42}, nullMask: []bool{false}}
		floatCol := &FloatColumn{data: []float64{3.14}, nullMask: []bool{false}}
		stringCol := &StringColumn{data: []string{"test"}, nullMask: []bool{false}}

		// Test valid access
		if val, err := intCol.GetValue(0); err != nil || val != int64(42) {
			t.Errorf("Expected 42, got %v with error %v", val, err)
		}
		if val, err := floatCol.GetValue(0); err != nil || val != 3.14 {
			t.Errorf("Expected 3.14, got %v with error %v", val, err)
		}
		if val, err := stringCol.GetValue(0); err != nil || val != "test" {
			t.Errorf("Expected 'test', got %v with error %v", val, err)
		}

		// Test out of bounds access
		_, err := intCol.GetValue(1)
		if err == nil || err.Error() != "index out of bounds" {
			t.Error("Expected out of bounds error for single element IntColumn")
		}
	})

	t.Run("all null columns", func(t *testing.T) {
		intCol := &IntColumn{data: []int64{1, 2, 3}, nullMask: []bool{true, true, true}}
		floatCol := &FloatColumn{data: []float64{1.1, 2.2, 3.3}, nullMask: []bool{true, true, true}}
		stringCol := &StringColumn{data: []string{"a", "b", "c"}, nullMask: []bool{true, true, true}}

		// All GetValue calls should return missing value error
		for i := 0; i < 3; i++ {
			_, err := intCol.GetValue(i)
			if err == nil || err.Error() != "missing value" {
				t.Errorf("Expected missing value error for IntColumn at index %d", i)
			}
			_, err = floatCol.GetValue(i)
			if err == nil || err.Error() != "missing value" {
				t.Errorf("Expected missing value error for FloatColumn at index %d", i)
			}
			_, err = stringCol.GetValue(i)
			if err == nil || err.Error() != "missing value" {
				t.Errorf("Expected missing value error for StringColumn at index %d", i)
			}
		}
	})
}
